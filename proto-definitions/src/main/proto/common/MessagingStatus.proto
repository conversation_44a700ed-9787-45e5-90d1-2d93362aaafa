syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.gcm";
option java_outer_classname = "MessagingStatusProto";
option objc_class_prefix = "GCM";

package id.co.plniconplus.legacy.gcm;

service GcmView {
  rpc GetListGcm (google.protobuf.Empty) returns (GcmResponseList) {}
}

message GcmResponse {
  int32 status = 1;
  string keterangan = 2;
}

message GcmResponseList {
  repeated GcmResponse gcmList = 1;
}