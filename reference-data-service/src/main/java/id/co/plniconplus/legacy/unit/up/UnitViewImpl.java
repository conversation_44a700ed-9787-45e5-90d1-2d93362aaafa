package id.co.plniconplus.legacy.unit.up;

import com.google.protobuf.Empty;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 10/09/2025
 */
@GrpcService
@RequiredArgsConstructor
public class UnitViewImpl implements UnitUpView {

    private final UnitUpRepo unitUpRepo;

    @Override
    public Uni<UnitUpResponseList> getListUnitUp(Empty request) {
        Log.info("Received request for getListUnitUp");

        return Uni.createFrom().item(() -> unitUpRepo.findAll().list())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    UnitUpResponseList.Builder builder = UnitUpResponseList.newBuilder();
                    list.forEach(uu -> builder.addUnitUpList(
                            UnitUpResponse.newBuilder()
                                    .setUnitUp(uu.unitUp)
                                    .setNamaUp(uu.namaUp)
                                    .build()
                    ));
                    return builder.build();
                });
    }
}
