package id.co.plniconplus.legacy.gcm;

import com.google.protobuf.Empty;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/16/2025
 */
@GrpcService
@RequiredArgsConstructor
public class MessagingStatusViewImpl implements GcmView {

    private final MessagingStatusRepo gcmRepo;

    @Override
    public Uni<GcmResponseList> getListGcm(Empty request) {
        Log.info("Received request for getListGcm");

        return Uni.createFrom().item(() -> gcmRepo.findAll().list())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    GcmResponseList.Builder builder = GcmResponseList.newBuilder();
                    list.forEach(ms -> builder.addGcmList(
                            GcmResponse.newBuilder()
                                    .setStatus(ms.status)
                                    .setKeterangan(ms.keterangan)
                                    .build()
                    ));
                    return builder.build();
                });
    }
}
