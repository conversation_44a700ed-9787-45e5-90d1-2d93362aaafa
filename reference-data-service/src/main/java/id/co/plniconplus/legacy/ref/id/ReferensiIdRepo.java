package id.co.plniconplus.legacy.ref.id;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/9/2025
 */
@ApplicationScoped
public class ReferensiIdRepo implements PanacheRepositoryBase<ReferensiId, String> {

    public Optional<ReferensiId> findWoByUnitUp(String unitUp) {
        return find("unitUp = ?1 and idPermohonan = 'WO'", unitUp).firstResultOptional();
    }
}
