package id.co.plniconplus.legacy.dayafasa;

import com.google.protobuf.Empty;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 13/08/2025
 */
@GrpcService
@RequiredArgsConstructor
public class DayaFasaViewImpl implements DayaFasaView {

    private final DayaFasaRepo dayaFasaRepo;

    @Override
    public Uni<DayaFasaResponseList> getListDayaFasa(Empty request) {
        Log.info("Received request for getListDayaFasa");

        return Uni.createFrom().item(() -> dayaFasaRepo.findAll().list())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    DayaFasaResponseList.Builder builder = DayaFasaResponseList.newBuilder();
                    list.forEach(df -> builder.addDayaFasaList(
                            DayaFasaResponse.newBuilder()
                                    .setDaya(df.daya)
                                    .setFasa(df.fasa)
                                    .build()
                    ));
                    return builder.build();
                });
    }
}
