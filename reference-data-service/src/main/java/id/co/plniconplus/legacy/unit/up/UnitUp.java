package id.co.plniconplus.legacy.unit.up;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 10/09/2025
 */
@Entity
@Table(schema = "FSO", name = "UNITUP")
public class UnitUp extends PanacheEntityBase {

    @Id
    @Size(max = 5)
    @Column(length = 5, name = "UNITUP")
    public String unitUp;

    @Size(max = 5)
    @Column(length = 5, name = "UNITAP")
    public String unitAp;

    @Size(max = 25)
    @Column(length = 25, name = "SATUAN")
    public String satuan;

    @Size(max = 30)
    @Column(length = 30, name = "NAMAUP")
    public String namaUp;

    @Size(max = 100)
    @Column(length = 100, name = "ALAMAT")
    public String alamat;

    @Size(max = 20)
    @Column(length = 20, name = "KOTA")
    public String kota;

    @Size(max = 20)
    @Column(length = 20, name = "TELP")
    public String telepon;

    @Size(max = 30)
    @Column(length = 30, name = "MANAGER")
    public String manager;

    @Size(max = 30)
    @Column(length = 30, name = "JABATAN")
    public String jabatan;

    @Size(max = 60)
    @Column(length = 60, name = "EMAIL_MANAGER")
    public String managerEmail;

    @Size(max = 30)
    @Column(length = 30, name = "LATITUDE")
    public String latitude;

    @Size(max = 30)
    @Column(length = 30, name = "LONGITUDE")
    public String longitude;
}
