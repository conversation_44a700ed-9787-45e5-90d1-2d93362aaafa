package id.co.plniconplus.legacy.tasrip;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
public record TasripDto(
        String ketPembTarif,
        String keterangan,
        String metKwh,
        String metKvarh,
        String metKvamaks,
        String teganganUkur,
        String faktorFrt,
        String faktorFjn,
        String statusEmin,
        String statusSubsidi,
        String statusDmp) {

    public static TasripDto fromEntity(Tasrip tasrip) {
        return new TasripDto(
                tasrip.ketPembTarif,
                tasrip.keterangan,
                tasrip.metKwh,
                tasrip.metKvarh,
                tasrip.metKvamaks,
                tasrip.teganganUkur,
                tasrip.faktorFrt,
                tasrip.faktorFjn,
                tasrip.statusEmin,
                tasrip.statusSubsidi,
                tasrip.statusDmp);
    }

    public Tasrip toEntity() {
        Tasrip tasrip = new Tasrip();
        tasrip.ketPembTarif = this.ketPembTarif;
        tasrip.keterangan = this.keterangan;
        tasrip.metKwh = this.metKwh;
        tasrip.metKvarh = this.metKvarh;
        tasrip.metKvamaks = this.metKvamaks;
        tasrip.teganganUkur = this.teganganUkur;
        tasrip.faktorFrt = this.faktorFrt;
        tasrip.faktorFjn = this.faktorFjn;
        tasrip.statusEmin = this.statusEmin;
        tasrip.statusSubsidi = this.statusSubsidi;
        tasrip.statusDmp = this.statusDmp;
        return tasrip;
    }
}
