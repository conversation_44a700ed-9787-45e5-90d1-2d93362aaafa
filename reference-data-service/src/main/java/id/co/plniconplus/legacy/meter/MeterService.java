package id.co.plniconplus.legacy.meter;

import id.co.plniconplus.legacy.meter.prepaid.MeterPrepaid;
import id.co.plniconplus.legacy.meter.prepaid.MeterPrepaidRepo;
import id.co.plniconplus.legacy.meter.prepaid.type.MeterPrepaidType;
import id.co.plniconplus.legacy.meter.prepaid.type.MeterPrepaidTypeRepo;
import id.co.plniconplus.legacy.meter.type.MeterType;
import id.co.plniconplus.legacy.meter.type.MeterTypeRepo;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/2/2025
 */
@ApplicationScoped
@RequiredArgsConstructor
public class MeterService {

    private final MeterRepo meterRepo;

    private final MeterTypeRepo meterTypeRepo;

    private final MeterPrepaidRepo meterPrepaidRepo;

    private final MeterPrepaidTypeRepo meterPrepaidTypeRepo;

    public Uni<Boolean> validateMeter(boolean isPrepaid, String merekMeter) {
        Log.infov("Received request for validateMeter: isPrepaid={0}, merekMeter={1}",
                isPrepaid, merekMeter);

        return Uni.createFrom().item(() -> validateMeterBlocking(isPrepaid, merekMeter))
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorv("Error validating meter: {0}", throwable.getMessage());

                    return false;
                });
    }

    private Boolean validateMeterBlocking(boolean isPrepaid, String merekMeter) {
        try {
            if (isPrepaid) {
                Optional<MeterPrepaid> meterPrepaid = meterPrepaidRepo.findByMerkMeter(merekMeter);
                if (meterPrepaid.isPresent()) {
                    Optional<MeterPrepaidType> meterPrepaidType = meterPrepaidTypeRepo.findByKodePabrikan(meterPrepaid.get().kodePabrikan);
                    return meterPrepaidType.isPresent();
                }
            } else {
                Optional<Meter> meter = meterRepo.findByMerkMeter(merekMeter);
                if (meter.isPresent()) {
                    Optional<MeterType> meterType = meterTypeRepo.findByMerekMeter(merekMeter);
                    return meterType.isPresent();
                }
            }
            return false;
        } catch (Exception e) {
            Log.errorv("Database error during meter validation: {0}", e.getMessage());

            return false;
        }
    }
}
