package id.co.plniconplus.legacy.harilibur;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_HARI_LIBUR")
public class HariLibur extends PanacheEntityBase {

    @Id
    @Column(name = "TGLLIBUR", columnDefinition = "DATE")
    public LocalDateTime tanggalLibur;

    @Size(max = 100)
    @Column(length = 100, name = "KETHARILIBUR")
    public String keteranganHariLibur;
}
