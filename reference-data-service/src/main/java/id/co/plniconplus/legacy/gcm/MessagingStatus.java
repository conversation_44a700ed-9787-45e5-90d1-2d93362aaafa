package id.co.plniconplus.legacy.gcm;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/10/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_GCM")
public class MessagingStatus extends PanacheEntityBase {

    @Id
    @Column(name = "STATUS")
    public Integer status;

    @Size(max = 50)
    @Column(length = 50, name = "KETERANGAN")
    public String keterangan;
}
