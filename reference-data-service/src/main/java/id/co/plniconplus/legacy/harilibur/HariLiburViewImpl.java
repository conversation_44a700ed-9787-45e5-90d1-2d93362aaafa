package id.co.plniconplus.legacy.harilibur;

import com.google.protobuf.Empty;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 15/08/2025
 */
@GrpcService
@RequiredArgsConstructor
public class HariLiburViewImpl implements HariLiburView {

    private final HariLiburRepo hariLiburRepo;

    @Override
    public Uni<HariLiburResponseList> getListHariLibur(Empty request) {
        Log.info("Received request for getListHariLibur");

        return Uni.createFrom().item(() -> hariLiburRepo.findAll().list())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    HariLiburResponseList.Builder builder = HariLiburResponseList.newBuilder();
                    list.forEach(hl -> builder.addHariLiburList(
                            HariLiburResponse.newBuilder()
                                    .setTanggalLibur(hl.tanggalLibur.toString())
                                    .setKeteranganHariLibur(hl.keteranganHariLibur)
                                    .build()
                    ));
                    return builder.build();
                });
    }

    @Override
    public Uni<HariLiburResponseList> getListHariLiburBetween(HariLiburBetweenRequest request) {
        Log.infov("Received request for getListHariLiburBetween: startDate={0}, endDate={1}",
                request.getStartDate(), request.getEndDate());

        return Uni.createFrom().item(() -> hariLiburRepo.findByTanggalLiburBetween(
                        LocalDateTime.parse(request.getStartDate()),
                        LocalDateTime.parse(request.getEndDate())).stream().toList())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    HariLiburResponseList.Builder builder = HariLiburResponseList.newBuilder();
                    list.forEach(hl -> builder.addHariLiburList(
                            HariLiburResponse.newBuilder()
                                    .setTanggalLibur(hl.tanggalLibur.toString())
                                    .setKeteranganHariLibur(hl.keteranganHariLibur)
                                    .build()
                    ));
                    return builder.build();
                });
    }

    @Override
    public Uni<HariLiburResponseList> getListHariLiburByDate(HariLiburRequest request) {
        Log.infov("Received request for getListHariLiburByDate: date={0}", request.getDate());

        return Uni.createFrom().item(() -> hariLiburRepo.findByTanggalLibur(
                        LocalDateTime.parse(request.getDate())).stream().toList())
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    HariLiburResponseList.Builder builder = HariLiburResponseList.newBuilder();
                    list.forEach(hl -> builder.addHariLiburList(
                            HariLiburResponse.newBuilder()
                                    .setTanggalLibur(hl.tanggalLibur.toString())
                                    .setKeteranganHariLibur(hl.keteranganHariLibur)
                                    .build()
                    ));
                    return builder.build();
                });
    }
}
