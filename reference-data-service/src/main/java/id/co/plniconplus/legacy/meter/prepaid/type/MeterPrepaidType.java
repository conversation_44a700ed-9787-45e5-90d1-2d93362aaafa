package id.co.plniconplus.legacy.meter.prepaid.type;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 26/08/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_TYPE_METER_PREPAID")
public class MeterPrepaidType extends PanacheEntityBase {

    @Id
    @Size(max = 15)
    @Column(length = 15, name = "TYPE_METER")
    public String meterType;

    @Size(max = 1)
    @Column(length = 1, name = "KODE_FASE", columnDefinition = "CHAR")
    public String kodeFase;

    @Column(name = "POWER_LIMIT", precision = 7, scale = 3)
    public BigDecimal powerLimit;

    @Size(max = 2)
    @Column(length = 2, name = "KODE_PABRIKAN", columnDefinition = "CHAR")
    public String kodePabrikan;

    @Size(max = 50)
    @Column(length = 50, name = "STANDAR_METER")
    public String standarMeter;

    @Size(max = 1)
    @Column(length = 1, name = "KODE_BLOK", columnDefinition = "CHAR")
    public String kodeBlok;

    @Column(name = "TANGGAL_BLOK",columnDefinition = "DATE")
    public LocalDateTime tanggalBlok;

    @Size(max = 100)
    @Column(length = 100, name = "KETERANGAN")
    public String keterangan;

    @Column(name = "TGLCATAT",columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;
}
