package id.co.plniconplus.legacy.meter.prepaid.type;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 26/08/2025
 */
@ApplicationScoped
public class MeterPrepaidTypeRepo implements PanacheRepositoryBase<MeterPrepaidType, String> {

    public Optional<MeterPrepaidType> findByKodePabrikan(String kodePabrikan) {
        return find("kodePabrikan", kodePabrikan).firstResultOptional();
    }
}
