package id.co.plniconplus.legacy.meter;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 27/08/2025
 */
@ApplicationScoped
public class MeterRepo implements PanacheRepositoryBase<Meter, String> {

    public Optional<Meter> findByMerkMeter(String merekMeter) {
        return find("merekMeter", merekMeter).firstResultOptional();
    }
}
