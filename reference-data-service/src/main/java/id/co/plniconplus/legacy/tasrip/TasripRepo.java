package id.co.plniconplus.legacy.tasrip;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/11/2025
 */
@ApplicationScoped
public class TasripRepo implements PanacheRepositoryBase<Tasrip, TasripId> {

    public List<TarifKwhDto> findAllTarifKwh() {
        return find("SELECT DISTINCT tarif, metKwh from Tasrip")
                .project(TarifKwhDto.class)
                .list();
    }
}
