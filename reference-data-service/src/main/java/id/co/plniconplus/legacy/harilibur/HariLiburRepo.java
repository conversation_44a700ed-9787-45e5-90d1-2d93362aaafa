package id.co.plniconplus.legacy.harilibur;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/6/2025
 */
@ApplicationScoped
public class HariLiburRepo implements PanacheRepositoryBase<HariLibur, LocalDateTime> {

    public List<HariLibur> findByTanggalLiburBetween(LocalDateTime tanggalAwal, LocalDateTime tanggalAkhir) {
        return find("tanggalLibur between ?1 and ?2", tanggalAwal, tanggalAkhir).list();
    }

    public List<HariLibur> findByTanggalLibur(LocalDateTime tanggal) {
        return find("tanggalLibur", tanggal).list();
    }
}
