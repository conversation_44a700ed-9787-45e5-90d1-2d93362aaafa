package id.co.plniconplus.legacy.tasrip;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/11/2025
 */
@ApplicationScoped
@RequiredArgsConstructor
public class TasripService {

    private final TasripRepo tasripRepo;

    public Multi<TasripDto> getAllByPage(int page, int size) {
        Log.infov("Find all tasrip page {0} size {1}", page, size);

        return Uni.createFrom().item(() -> {
                    Log.info("Executing database query on worker thread");

                    return tasripRepo.findAll().page(page, size).list();
                })
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().invoke(list -> Log.infov("Retrived {0} items from database", list.size()))
                .onItem().transformToMulti(list ->
                        Multi.createFrom().iterable(list))
                .onItem().transform(TasripDto::fromEntity)
                .onFailure().invoke(e -> Log.error("Failed to retrieve tasrip", e));
    }
}
