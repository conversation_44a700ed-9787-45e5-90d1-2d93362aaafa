package id.co.plniconplus.legacy.tasrip;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/1/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_TASRIP")
public class Tasrip extends PanacheEntityBase {

    @EmbeddedId
    public TasripId tasripId;

    @Size(max = 100)
    @Column(length = 100, name = "KETPEMBTRF")
    public String ketPembTarif;

    @Size(max = 100)
    @Column(length = 100, name = "KETERANGAN")
    public String keterangan;

    @Size(max = 2)
    @Column(length = 2, name = "METKWH")
    public String metKwh;

    @Size(max = 2)
    @Column(length = 2, name = "METKVARH")
    public String metKvarh;

    @Size(max = 2)
    @Column(length = 2, name = "METKVAMAKS")
    public String metKvamaks;

    @Size(max = 10)
    @Column(length = 10, name = "TEGUKUR")
    public String teganganUkur;

    @Size(max = 10)
    @Column(length = 10, name = "FAKTORFRT")
    public String faktorFrt;

    @Size(max = 10)
    @Column(length = 10, name = "FAKTORFJN")
    public String faktorFjn;

    @Size(max = 10)
    @Column(length = 10, name = "STATUSEMIN")
    public String statusEmin;

    @Size(max = 10)
    @Column(length = 10, name = "STATUSSUBSIDI")
    public String statusSubsidi;

    @Size(max = 10)
    @Column(length = 10, name = "STATUSDMP")
    public String statusDmp;
}
