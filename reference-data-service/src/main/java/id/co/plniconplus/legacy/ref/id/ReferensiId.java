package id.co.plniconplus.legacy.ref.id;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/9/2025
 */
@Entity
@Table(schema = "FSO", name = "REF_ID")
public class ReferensiId extends PanacheEntityBase {

    @Id
    @Size(max = 10)
    @Column(length = 10, name = "ID_PERMOHONAN")
    public String idPermohonan;

    @Size(max = 5)
    @Column(length = 5, name = "UNITUP")
    public String unitUp;

    @Column(name = "NO_URUTTERAKHIR", columnDefinition = "NUMBER")
    public Long noUrutTerakhir;

    @Column(name = "NO_URUT", columnDefinition = "NUMBER")
    public Long noUrut;

    @Column(name = "TGL_AKHIR_GENERATE", columnDefinition = "DATE")
    public LocalDateTime tanggalAkhirGenerate;
}
