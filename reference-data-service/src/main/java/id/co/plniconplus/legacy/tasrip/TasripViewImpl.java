package id.co.plniconplus.legacy.tasrip;

import com.google.protobuf.Empty;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/11/2025
 */
@GrpcService
@RequiredArgsConstructor
public class TasripViewImpl implements TasripView {

    private final TasripRepo tasripRepo;

    @Override
    public Uni<TarifKwhList> getTarifKwh(Empty request) {
        return Uni.createFrom().item(tasripRepo::findAllTarifKwh)
                .emitOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(list -> {
                    TarifKwhList.Builder builder = TarifKwhList.newBuilder();
                    list.stream()
                            .filter(Objects::nonNull)
                            .forEach(tk -> builder.addTarifKwhList(
                                    TarifKwh.newBuilder()
                                            .setTarif(tk.tarif())
                                            .setMetKwh(tk.metKwh())
                                            .build()
                            ));
                    return builder.build();
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorv("Error getting tarif kwh: {0}", throwable.getMessage());

                    return TarifKwhList.newBuilder().build();
                });
    }
}
