package id.co.plniconplus.legacy.dayafasa;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
@Entity
@Table(schema = "FSO", name = "REF_DAYA_FASA")
public class DayaFasa extends PanacheEntityBase {

    @Id
    @Column(name = "DAYA")
    public Integer daya;

    @Column(name = "FASA")
    public Integer fasa;
}
