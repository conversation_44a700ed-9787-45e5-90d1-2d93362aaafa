package id.co.plniconplus.legacy.ref.id;

import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/16/2025
 */
@GrpcService
@RequiredArgsConstructor
public class ReferensiIdViewImpl implements ReferensiIdView {

    private final ReferensiIdRepo refIdRepo;

    @Override
    public Uni<NoWo> generateNoWo(UnitUp request) {
        Log.infov("Request generate no wo: {0}", request.getKode());

        return Uni.createFrom().item(() -> performSequenceLogic(request.getKode()))
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .map(noUrut -> {
                    String formattedDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
                    String paddedNoUrut = String.format("%04d", noUrut);
                    String generatedNoWo = String.format("WO/%s/%s/%s", request.getKode(), formattedDate, paddedNoUrut);
                    return NoWo.newBuilder()
                            .setGenerated(generatedNoWo)
                            .build();
                });
    }

    @Transactional
    protected long performSequenceLogic(String unitUp) {
        Optional<ReferensiId> woByUnitUp = refIdRepo.findWoByUnitUp(unitUp);
        ReferensiId ref;

        if (woByUnitUp.isPresent()) {
            ref = woByUnitUp.get();
            ref.noUrut = ref.noUrut + 1;
        } else {
            ref = new ReferensiId();
            ref.idPermohonan = "WO";
            ref.unitUp = unitUp;
            ref.noUrut = 1L;
        }

        ref.noUrutTerakhir = ref.noUrut;
        ref.tanggalAkhirGenerate = LocalDateTime.now();
        refIdRepo.persistAndFlush(ref);
        return ref.noUrut;
    }
}
