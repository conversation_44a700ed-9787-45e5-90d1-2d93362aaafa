quarkus:
  http:
    root-path: /api/references
  hibernate-orm:
    log:
      sql: true
      bind-parameters: true

"%test":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ***********************************
      username: ${REF_DATA_SERVICE_DATASOURCE_USERNAME}
      password: ${REF_DATA_SERVICE_DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${REF_DATA_SERVICE_GRPC_HOST}
        port: ${REF_DATA_SERVICE_GRPC_PORT}
        enable-reflection-service: true

"%dev":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ***************************************
      username: ${REF_DATA_SERVICE_DATASOURCE_USERNAME}
      password: ${REF_DATA_SERVICE_DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${REF_DATA_SERVICE_GRPC_HOST}
        port: ${REF_DATA_SERVICE_GRPC_PORT}
        enable-reflection-service: true

"%prod":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ${REF_DATA_SERVICE_DATASOURCE_JDBC_URL}
      username: ${REF_DATA_SERVICE_DATASOURCE_USERNAME}
      password: ${REF_DATA_SERVICE_DATASOURCE_PASSWORD}
    hibernate-orm:
      log:
        sql: false
        bind-parameters: false
    grpc:
      server:
        host: ${REF_DATA_SERVICE_GRPC_HOST}
        port: ${REF_DATA_SERVICE_GRPC_PORT}
        enable-reflection-service: true
