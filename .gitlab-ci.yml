stages:
  - build
  - native-image
  - dockerize
  - deploy

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  REGISTRY_IMAGE: $CI_REGISTRY_IMAGE/reference-data-service
  DOCKER_DRIVER: overlay2
  REF_DATA_SERVICE_GRPC_HOST: $REF_DATA_SERVICE_GRPC_HOST
  REF_DATA_SERVICE_GRPC_PORT: $REF_DATA_SERVICE_GRPC_PORT
  REF_DATA_SERVICE_DATASOURCE_JDBC_URL: $REF_DATA_SERVICE_DATASOURCE_JDBC_URL
  REF_DATA_SERVICE_DATASOURCE_USERNAME: $REF_DATA_SERVICE_DATASOURCE_USERNAME
  REF_DATA_SERVICE_DATASOURCE_PASSWORD: $REF_DATA_SERVICE_DATASOURCE_PASSWORD

cache:
  paths:
    - .m2/repository

before_script:
  - echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY

deploy_proto_definitions:
  stage: deploy
  image: maven:3.9.11-amazoncorretto-21
  script:
    - echo "Changes detected in proto-definitions. Building and deploying."
    - cd proto-definitions
    - mvn deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - proto-definitions/**/*

deploy_api_contracts:
  stage: deploy
  image: maven:3.9.11-amazoncorretto-21
  script:
    - echo "Changes detected in api-contracts. Building and deploying."
    - cd api-contracts
    - mvn deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - api-contracts/**/*

build_reference_data_service:
  stage: build
  image: quay.io/quarkus/ubi-quarkus-native-image:23.1-java21
  script:
    - echo "Building reference-data-service..."
    - cd reference-data-service
    - ./mvnw clean install -DskipTests
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - reference-data-service/**/*

native_reference_data_service:
  stage: native-image
  image: quay.io/quarkus/ubi-quarkus-native-image:23.1-java21
  script:
    - echo "Packaging native image for reference-data-service..."
    - cd reference-data-service
    - ./mvnw package -Pnative -DskipTests -Dquarkus.native.builder-image=quay.io/quarkus/ubi-quarkus-mandrel:23.1-java21
  artifacts:
    paths:
      - reference-data-service/target/*-runner
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - reference-data-service/**/*

dockerize_reference_data_service:
  stage: dockerize
  image: docker:latest
  services:
    - docker:dind
  script:
    - cd reference-data-service
    - docker build -f src/main/docker/Dockerfile.native -t $REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA .
    - docker tag $REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA $REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - reference-data-service/**/*

test_native_reference_data_service:
  stage: dockerize
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker run --rm $REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA --help
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual

deploy_reference_data_service:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - echo "Deploying reference-data-service to GitLab Container Registry..."
    - docker push $REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    - docker push $REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
