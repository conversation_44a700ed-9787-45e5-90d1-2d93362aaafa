services:
  reference-data-service:
    image: reference-data-service:local
    build:
      context: ./reference-data-service
      dockerfile: src/main/docker/Dockerfile.native
    ports:
      - "8080:8080"   # HTTP endpoint
      - "9000:9000"   # gRPC endpoint
    depends_on:
      - oracle11g
    environment:
      REF_DATA_SERVICE_GRPC_HOST: "localhost"
      REF_DATA_SERVICE_GRPC_PORT: "9000"
      REF_DATA_SERVICE_DATASOURCE_JDBC_URL: "***********************************"
      REF_DATA_SERVICE_DATASOURCE_USERNAME: "system"
      REF_DATA_SERVICE_DATASOURCE_PASSWORD: "oracle"
    restart: unless-stopped

  oracle11g:
    image: oracleinanutshell/oracle-xe-11g
    ports:
      - "1521:1521"
    environment:
      ORACLE_ALLOW_REMOTE: true
    volumes:
      - oracle-data:/u01/app/oracle
    restart: unless-stopped

volumes:
  oracle-data:
